<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-trans-support-upm</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-trans-support-upm-api</artifactId>
    <version>${genn-trans-support-upm-api.version}</version>

    <properties>
        <sa-token.version>1.37.0</sa-token.version>
        <genn-trans-support-upm-api.version>1.0.0-SNAPSHOT</genn-trans-support-upm-api.version>
    </properties>

    <profiles>
        <!--  自身api包管理,只需要修改pro和上面的local即可       -->
        <profile>
            <id>pro</id>
            <properties>
                <genn-trans-support-upm-api.version>1.1.9-RELEASE</genn-trans-support-upm-api.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>cn.genn</groupId>
            <artifactId>genn-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
