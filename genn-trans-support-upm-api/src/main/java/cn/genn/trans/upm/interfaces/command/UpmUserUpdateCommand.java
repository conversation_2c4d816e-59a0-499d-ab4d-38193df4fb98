package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class UpmUserUpdateCommand {

    @ApiModelProperty(value = "id")
    @NotNull(message = "用户id不能为空")
    private Long id;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "昵称")
    @Size(max = 32, message = "昵称最大长度不能超过32")
    private String nick;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "邮箱")
    @Pattern(regexp="^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message="邮箱格式错误")
    private String email;

    @ApiModelProperty(value = "关联角色id")
    private List<Long> roleIdList;

    /**
     * 大部分是true;false用于未构建完成的超管用户
     */
    @ApiModelProperty(value = "更新是否清除原有角色")
    private Boolean clearSign;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "销售名称")
    private String saleName;

    @ApiModelProperty(value = "账号有效期状态:true:存在有效期,false:无有效期")
    private Boolean effectiveTimeEnable = true;

    @ApiModelProperty(value = "账号有效期")
    private LocalDateTime effectiveTime;
}
