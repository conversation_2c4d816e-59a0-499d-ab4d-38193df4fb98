package cn.genn.trans.upm.infrastructure.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 * <AUTHOR>
 */
public enum MessageCode implements MessageCodeWrap {

    RESOURCE_NOT_EXIST("201", "资源不存在"),
    RESOURCE_NAME_REPEAT("202","相同层级中已存在同名菜单"),
    RESOURCE_PATH_REPEAT("203","资源路径已存在"),
    NO_UPDATE_ERROR("204","不存在要修改的数据"),

    SYSTEM_CODE_EXIST_ERROR("301", "系统编码已存在"),
    SYSTEM_CODE_NOT_EXIST_ERROR("302", "当前系统不存在"),
    SYSTEM_CODE_ERROR("303", "系统编码不能为空"),
    SYSTEM_TENANT_REL_NOT_EXIST("304","请检查系统租户关联表配置"),
    SYSTEM_OTHER_NOT_CHANGE("305","禁止维护其他系统数据"),

    RESOURCE_BIND_SYSTEM_NOT_EXIST_ERROR("401", "资源所属系统不存在"),
    RESOURCE_CODE_EXIST_ERROR("402", "当前系统资源编码已存在"),
    RESOURCE_NOT_EXIST_ERROR("403", "当前系统资源不存在"),
    ROLE_EXIST_ERROR("404","角色已存在"),
    ROLE_NOT_EXIST_ERROR("405","角色不存在"),
    ROLE_SYSTEM_NOT_DELETE_ERROR("406","系统角色禁止删除"),
    RESOURCE_STOP_EDIT("407","当前资源禁止操作,请联系相关人员"),
    ROLE_CODE_EXIST_ERROR("408","角色编码已存在"),
    ROLE_NAME_REPEAT("409","角色名称重复"),
    ROLE_CODE_REPEAT("410","角色编码重复"),
    USER_EXPIRED("425","账号已过有效期"),

    USER_NOT_LOGIN("501", "当前用户未登录"),
    USER_NOT_EXIST("502", "用户名或密码错误,请重新输入!"),
    USER_TICKET_NOT_EXIST("503", "用户当前ticket码已失效,请重新进行登录"),
    USER_UN_BIND_ACCOUNT("504", "用户校验失败,当前账号不存在或已冻结"),
    USER_GENERATE_TOKEN_FAIL("505", "用户生成token失败"),
    USER_FREEZE("506","用户已冻结"),
    USER_CAPTCHA_FAIL("507","验证码校验失败"),
    USER_EXIST("508","用户已存在"),
    USER_NOT_EXIST_ERROR("509","用户不存在"),
    ACCOUNT_DELETE_ERROR("510","系统账号禁止删除"),
    PASSWORD_CHECK_ERROR("511","密码不符合规范"),
    USER_DISABLE("512","用户已停用"),
    USER_SEND_SMS_FAIL("513","短信验证码发送失败,超出每天上限"),
    USER_SEND_SMS_FAIL_TIME("513","短信验证码发送失败,每次获取验证码需间隔一分钟"),
    USER_SMS_CODE_EXPIRE("514", "验证码已失效，请重新获取验证码!"),
    USER_SMS_CODE_CHECK_FAIL("515", "验证码错误，请输入正确的验证码!"),
    PASSWORD_CHECK_FAIL("516", "新密码与确认密码不一致"),
    PASSWORD_CHECK_FAIL2("517", "新密码与原密码一致"),
    PASSWORD_CHECK_FAIL3("518", "原密码与登录密码不一致"),


    MINI_APP_ID_NOT_EXIST("519","请在服务端配置小程序"),
    MINI_USER_INFO_EXIST("520","获取用户信息失败"),
    MINI_PHONE_EXIST("521","获取手机号失败"),
    PHONE_EXIST("522","手机号已存在"),
    TELEPHONE_BIND_EXIST("523","用户已绑定手机号"),
    TELEPHONE_BIND_NOT_EXIST("524","用户不存在已绑定的手机号"),
    TELEPHONE_BIND_ERROR("525","用户已绑定手机与原密保手机不一致"),
    TELEPHONE_BIND_EQUAL("526","原密保手机与新密保手机一致"),
    TELEPHONE_USER_BIND_EXIST("527","手机号已被使用"),
    FEISHU_USER_INFO_ERROR("528","获取用户信息失败"),
    FEISHU_LOGIN_FAIL("529","飞书登录失败"),

    AUTH_GROUP_PL_NOT_INSET("601","禁止维护平台组织"),
    AUTH_TYPE_NOT_EXIST("602","未查询到该组织类型配置"),
    AUTH_TYPE_RESOURCE_NOT_EXIST("602","未查询到该组织类型的菜单配置"),
    AUTH_TYPE_ROLE_CODE_EXIST("604","角色编码已存在"),
    AUTH_TYPE_ROLE_NAME_EXIST("605","角色名称已存在"),
    AUTH_TYPE_ROLE_NOT_EXIST_ERROR("606","角色模版不存在"),

    SYSTEM_REOURCE_NOT_EXIST("700", "未查询到系统资源信息，暂不支持创建管理员"),
    SYSTEM_REOURCE_NOT_EXIST_CANT_CREATE_ROLE("701", "未查询到系统资源信息，暂不支持创建角色"),
    SYSTEM_REOURCE_NOT_EXIST_CANT_UPDATE_ROLE("702", "未查询到系统资源信息，暂不支持修改角色"),
    SYSTEM_REOURCE_NOT_EXIST_CANT_CREATE_USER("703", "未查询到系统资源信息，暂不支持创建用户"),
    SYSTEM_REOURCE_NOT_EXIST_CANT_UPDATE_USER("704", "未查询到系统资源信息，暂不支持修改用户"),
    SYSTEM_REOURCE_NOT_EXIST_CANT_DEL_USER("705", "未查询到系统资源信息，暂不支持删除用户"),
    ACCOUNT_NOT_EXIST_CANT_UPDATE_USER("706", "未查询到账户信息，暂不支持修改用户"),


    REFLECTION_OPERATION_BEFORE_STRING_TYPE_QUERY_FAILED("800", "字符串类型查询前反射操作失败"),


    NOT_YZG_SYNC_RESOURCE_CANT_DEL_RESOURCE("850", "非站端同步油掌柜资源，不可删除资源"),

    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getBizCode() {
        return DEFAULT_BIZ_CODE;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
