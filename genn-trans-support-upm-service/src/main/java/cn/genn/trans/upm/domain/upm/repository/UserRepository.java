package cn.genn.trans.upm.domain.upm.repository;


import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserRepository {

    /**
     * 查询 账号根据用户名
     *
     * @param userName
     * @return
     */
    UpmAccount queryAccountByUserName(String userName,Long systemId);
    UpmAccount queryAccountByTelephone(String telephone,Long systemId);

    /**
     * 查询 账号根据用户名
     *
     * @param accountId
     * @return
     */
    UpmAccount queryAccountById(Long accountId);

    /**
     * 查询 账号id查询用户列表
     *
     * @param accountId
     * @return
     */
    List<UpmUser> queryListByAccountId(Long accountId);

    /**
     * 账号id和vpcGroup查询用户列表
     */
    List<UpmUser> queryListByAccountIdAndVpcGroup(Long accountId,String vpcGroup);

    /**
     * 查询用户绑定的角色及权限
     * @return
     */
    UpmUser findUserRoleResource(Long userId);

    /**
     * 查询所有用户及用户角色
     * @return
     */
    List<UpmUser> findAllUserAndRole();

    /**
     * 租户id查询系统
     * @param tenantId
     * @return
     */
    List<UpmUserPO> selectByTenantId(Long tenantId);

    /**
     * id查询用户
     */
    UpmUserPO selectById(Long id);

    List<UpmUserPO> selectByIds(List<Long> ids);

    /**
     * 修改
     *
     * @return Boolean
     */
    Boolean update(UpmUserPO upmUserPO,Boolean effectiveTimeEnable);

    /**
     * 批量修改状态
     */
    Long updateStatus(List<Long> idList, StatusEnum status);

    /**
     * 批量删除
     */
    Boolean batchDelete(List<Long> idList);

    boolean saveBatchList(List<UpmUserPO> list);

    boolean saveBatchPOList(List<UpmUserPO> list);

    Boolean changeUserInfo(UpmUser upmUser);

    Boolean bindPhone(UpmUser upmUser);

    Long createUser(UpmUser user);
}
