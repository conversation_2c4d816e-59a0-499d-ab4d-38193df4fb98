package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.assembler.UpmUserAssembler;
import cn.genn.trans.upm.application.assembler.UpmUserThirdAssembler;
import cn.genn.trans.upm.application.dto.UserAccountDTO;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.infrastructure.utils.QryUtil;
import cn.genn.trans.upm.infrastructure.utils.SqlEscapeUtil;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FsExtraInfoDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FsUserDTO;
import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import cn.genn.trans.upm.interfaces.query.*;
import cn.genn.trans.upm.interfaces.query.mini.UpmUserInfoQuery;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmUserQueryService {

    @Resource
    private UpmUserMapper userMapper;
    @Resource
    private UpmUserAssembler assembler;
    @Resource
    private UpmRoleQueryService roleQueryService;
    @Resource
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private UpmUserThirdAssembler userThirdAssembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmUserDTO分页对象
     */
    public PageResultDTO<UpmUserDTO> page(UpmUserPageQuery query) {
        // 特殊字符格式转换
        if (StrUtil.isNotBlank(query.getUsername())) {
            query.setUsername(SqlEscapeUtil.escape(query.getUsername()));
        }
        if (StrUtil.isNotBlank(query.getNick())) {
            query.setNick(SqlEscapeUtil.escape(query.getNick()));
        }
        if (StrUtil.isNotBlank(query.getTelephone())) {
            query.setTelephone(SqlEscapeUtil.escape(query.getTelephone()));
        }
        String authKey = CurrentUserHolder.getAuthKey();
        Page<UpmUserDTO> resultPage = userMapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query, authKey);
        if (CollectionUtil.isEmpty(resultPage.getRecords())) {
            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), Collections.emptyList());
        }
        List<Long> userIdList = resultPage.getRecords().stream().map(UpmUserDTO::getId).collect(Collectors.toList());
        List<UpmRoleUserDTO> upmRoleDTOS = roleQueryService.queryByUserIds(userIdList);
        Map<Long, List<UpmRoleUserDTO>> RoleMap = upmRoleDTOS.stream().collect(Collectors.groupingBy(UpmRoleUserDTO::getUserId));
        for (UpmUserDTO record : resultPage.getRecords()) {
            record.setRoleList(RoleMap.get(record.getId()));
        }
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), resultPage.getRecords());
    }


    public PageResultDTO<UpmUserDTO> pageUserRole(UpmUserRolePageQuery query) {
        query = QryUtil.operateBeforeQuery(query);
        String authKey = CurrentUserHolder.getAuthKey();
        Page<UpmUserDTO> resultPage = userMapper.selectUserRoleByPage(new Page<>(query.getPageNo(), query.getPageSize()), query, authKey);
        if (CollectionUtil.isEmpty(resultPage.getRecords())) {
            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), Collections.emptyList());
        }
        List<Long> userIdList = resultPage.getRecords().stream().map(UpmUserDTO::getId).collect(Collectors.toList());
        List<UpmRoleUserDTO> upmRoleDTOS = roleQueryService.queryByUserIds(userIdList);
        Map<Long, List<UpmRoleUserDTO>> RoleMap = upmRoleDTOS.stream().collect(Collectors.groupingBy(UpmRoleUserDTO::getUserId));
        for (UpmUserDTO record : resultPage.getRecords()) {
            record.setRoleList(RoleMap.get(record.getId()));
        }
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), resultPage.getRecords());
    }

    /**
     * 查询用户列表
     */
    public List<UpmUserDTO> conditionList(UpmUserQuery query) {
        List<UpmUserDTO> upmUserDTOS = userMapper.selectByQuery(query);
        if(CollUtil.isEmpty(upmUserDTOS)){
            return upmUserDTOS;
        }
        List<Long> userIds = upmUserDTOS.stream().map(UpmUserDTO::getId).distinct().collect(Collectors.toList());
        //补充三方信息
        List<UpmUserThirdPO> upmUserThirdPOS = userThirdMapper.selectEnableUser(userIds, ThridTypeEnum.FS);
        Map<Long, UpmUserThirdPO> userThirdMap = upmUserThirdPOS.stream()
            .collect(Collectors.toMap(
                UpmUserThirdPO::getUserId, // key: userId
                Function.identity(),
                (existing, replacement) -> {
                    // 如果existing没有extraInfo，而replacement有，则用replacement替换
                    if (StrUtil.isBlank(existing.getExtraInfo()) && StrUtil.isNotBlank(replacement.getExtraInfo())) {
                        return replacement;
                    }
                    // 否则保留原有的记录
                    return existing;
                }
            ));
        for (UpmUserDTO upmUserDTO : upmUserDTOS) {
            UpmUserThirdPO upmUserThirdPO = userThirdMap.get(upmUserDTO.getId());
            if(ObjUtil.isNotEmpty(upmUserThirdPO)){
                FsUserDTO fsUserDTO = FsUserDTO.builder()
                    .fsAppId(upmUserThirdPO.getAppId())
                    .fsOpenId(upmUserThirdPO.getOpenId())
                    .fsName(upmUserDTO.getNick())
                    .fsTelephone(upmUserDTO.getTelephone())
                    .build();
                if(StrUtil.isNotBlank(upmUserThirdPO.getExtraInfo())){
                    FsExtraInfoDTO parse = JsonUtils.parse(upmUserThirdPO.getExtraInfo(), FsExtraInfoDTO.class);
                    fsUserDTO.setFsAvatarUrl(parse.getAvatarUrl());
                    fsUserDTO.setFsDepartments(parse.getDepartments());
                }
                upmUserDTO.setFsUser(fsUserDTO);
            }
        }
        return upmUserDTOS;
    }

    /**
     * usernameList和authKey查询用户
     * @return
     */
    public List<UpmUserDTO> queryUserByUsernamesAndAuthKey(UpmUserInfoQuery query) {
        List<String> usernameList = query.getUsernameList();
        String authKey = query.getAuthKey();
        Long systemId = AuthKeyUtil.getSystemId(authKey);
        return userMapper.queryUserByUsernamesAndAuthKey(usernameList,authKey,systemId);
    }

    /**
     * search检索username和nick查询用户列表,仅限当前权限组
     * @param search
     * @return
     */
    public List<UpmUserDTO> searchName(String search,String authKey) {
        Long systemId = AuthKeyUtil.getSystemId(authKey);
        return userMapper.searchName(search,authKey,systemId);
    }

    /**
     * 角色code查询用户
     * @param query
     * @return
     */
    public List<UpmUserDTO> queryByRoleCode(UserRoleQuery query) {
        if (query.getRoleCode().equals(upmSeverProperties.getNormalRoleCode())){
            return new ArrayList<>();
        }
        return userMapper.queryByRoleCode(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmUserDTO
     */
    public UpmUserDTO get(Long id) {
        UpmUserDTO result = userMapper.selectByUserId(id);
        // 补充角色
        List<UpmRoleUserDTO> upmRoleDTOS = roleQueryService.queryByUserIds(Collections.singletonList(result.getId()));
        if (CollectionUtil.isNotEmpty(upmRoleDTOS)) {
            result.setRoleList(upmRoleDTOS);
        }
        return result;
    }

    /**
     * username判断账号是否存在,true存在
     */
    public boolean checkUserName(String username, Long systemId) {
        List<UpmUserPO> upmUserPOS = userMapper.selectByUsernameAndSystemId(username, systemId);
        return CollUtil.isNotEmpty(upmUserPOS);
    }

    /**
     * 手机号判断账号是否存在,true存在
     *
     * @param telephone
     * @return
     */
    public Boolean checkTelephone(String telephone, Long systemId) {
        List<UpmUserPO> upmUserPOS = userMapper.selectByTelephoneAndSystemId(telephone, systemId);
        return CollUtil.isNotEmpty(upmUserPOS);
    }

    /**
     * id查询用户
     *
     * @param idList
     * @return
     */
    public List<UserAccountDTO> selectUserList(List<Long> idList) {
        return userMapper.selectUserList(idList);
    }

    /**
     * userIds获取三方用户信息
     * @param query
     * @return
     */
    public List<UpmUserThirdDTO> getThirdByUserIds(UserThirdQuery query){
        List<UpmUserThirdPO> upmUserThirdPOS = userThirdMapper.selectByAppIdAndUserIds(query.getThirdType(), query.getAppId(), query.getUserIdList());
        return userThirdAssembler.PO2DTO(upmUserThirdPOS);
    }

    public List<UpmUserThirdDTO> getThirdByUserIdsAndSystemType(UserThirdSystemQuery query){
        return userThirdMapper.getThirdByUserIdsAndSystemType(query);
    }


    public UpmUserDTO queryByFsUser(UserThirdQuery query) {
        UpmUserThirdPO upmUserThirdPO = userThirdMapper.queryByFsUser(query);
        if(upmUserThirdPO!=null){
            Long userId = upmUserThirdPO.getUserId();
            return get(userId);
        }
        return null;
    }
}

