package cn.genn.trans.upm.application.processor;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.application.service.action.SsoAccountActionService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.CheckRulePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.utils.RSAUtil;
import cn.genn.trans.upm.infrastructure.utils.SmsUtil;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import cn.genn.trans.upm.interfaces.enums.PasswordCheckEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 用户校验
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
@Component
@Slf4j
public class UserProcessor {

    @Resource
    private UserRepository userRepository;
    @Resource
    private UpmAccountMapper accountMapper;
    @Resource
    private UpmTenantSystemMapper upmTenantSystemMapper;
    @Resource
    private SsoAuthProperties ssoAuthProperties;
    @Resource
    private SystemRepository systemRepository;
    @Resource
    private SsoAccountActionService ssoAccountActionService;

    public void checkSave(UpmUserSaveCommand command){
        //账号校验
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(command.getUsername(), CurrentUserHolder.getSystemId());
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        //手机号校验
        if(StrUtil.isNotBlank(command.getTelephone())){
            upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),CurrentUserHolder.getSystemId());
            if(ObjUtil.isNotNull(upmAccountPO)){
                throw new BusinessException(MessageCode.PHONE_EXIST);
            }
        }
        this.passwordCheck(command.getPassword());
    }

    public void checkSaveNoLogin(UpmUserNoLoginSaveCommand command){
        //账号校验
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(command.getUsername(), AuthKeyUtil.getSystemId(command.getAuthKey()));
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        //手机号校验
        if(StrUtil.isNotBlank(command.getTelephone())){
            upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),AuthKeyUtil.getSystemId(command.getAuthKey()));
            if(ObjUtil.isNotNull(upmAccountPO)){
                throw new BusinessException(MessageCode.PHONE_EXIST);
            }
        }
        this.passwordCheckNoLogin(command.getPassword(),command.getAuthKey());
    }

    public void checkBeforeSaveBySystemType(UpmUserSaveBySystemTypeCommand command){
        this.passwordCheck(command.getPassword());
    }

    public UpmUserPO checkUpdate(UpmUserUpdateCommand command){
        // 数据校验
        UpmUserPO upmUserPO = userRepository.selectById(command.getId());
        if (Objects.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 手机号校验
        if (StrUtil.isNotBlank(upmUserPO.getTelephone()) && StrUtil.isNotBlank(command.getTelephone())
            && !upmUserPO.getTelephone().equals(command.getTelephone())) {
            //手机号校验
            UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),CurrentUserHolder.getSystemId());
            if(ObjUtil.isNotNull(upmAccountPO)){
                throw new BusinessException(MessageCode.PHONE_EXIST);
            }
        }
        return upmUserPO;
    }

    public void checkUserRegister(UserRegisTerCommand command){
        String password = this.decryptPassword(command.getPassword());
        String confirmPassword = this.decryptPassword(command.getConfirmPassword());
        //密码不同
        if(!password.equals(confirmPassword)){
            throw new CheckException(MessageCode.PASSWORD_CHECK_ERROR);
        }
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(command.getUsername(), command.getSystemId());
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        //手机号校验
        upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),command.getSystemId());
        if(ObjUtil.isNotNull(upmAccountPO)){
            throw new BusinessException(MessageCode.PHONE_EXIST);
        }
        if(!password.matches(PasswordCheckEnum.STRONG.getRegular())){
            throw new BusinessException(MessageCode.PASSWORD_CHECK_ERROR);
        }
        // 校验输入的短信验证码
        SmsUtil.checkTelephone(SmsUtil.BUSINESS_CODE_OF_REGISTER,command.getTelephone(),command.getCaptcha());

    }

    public UpmSystem checkDriverRegister(DriverRegisterCommand command){
        //系统校验
        UpmSystem upmSystem = systemRepository.find(command.getSystemCode());
        if(ObjUtil.isNull(upmSystem)){
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }
        //账号校验
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(command.getTelephone(),upmSystem.getId());
        if(ObjUtil.isNotNull(upmAccountPO)){
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        //手机号校验
        upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),upmSystem.getId());
        if(ObjUtil.isNotNull(upmAccountPO)){
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        return upmSystem;

    }

    public UpmUserPO checkResetPassword(UpmUserResetPasswordCommand command){
        UpmUserPO upmUserPO = userRepository.selectById(command.getId());
        if (Objects.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        //密码规则校验
        this.passwordCheck(command.getNewPassword());
        return upmUserPO;
    }

    /**
     * 密码校验
     * @param password
     * @return
     */
    private void passwordCheck(String password){
        Long systemId = CurrentUserHolder.getSystemId();
        checkPasswordBySystemId(systemId, password);
    }

    private void passwordCheckNoLogin(String password,String authKey){
        Long tenantId = AuthKeyUtil.getTenantId(authKey);
        if(StrUtil.isBlank(password)){
            return;
        }
        UpmTenantSystemPO tenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(tenantId,AuthKeyUtil.getSystemId(authKey));
        if(ObjUtil.isNull(tenantSystemPO)){
            throw new BusinessException(MessageCode.SYSTEM_TENANT_REL_NOT_EXIST);
        }
        CheckRulePO checkRule = tenantSystemPO.getCheckRule();
        String regular = PasswordCheckEnum.STRONG.getRegular();
        if (ObjUtil.isNotNull(checkRule) && ObjUtil.isNotNull(checkRule.getPasswdCheckType())) {
            regular = PasswordCheckEnum.of(checkRule.getPasswdCheckType()).getRegular();
        }
        if(!password.matches(regular)){
            throw new BusinessException(MessageCode.PASSWORD_CHECK_ERROR);
        }
    }

    public void checkPasswordBySystemId(Long systemId, String password){
        Long tenantId = CurrentUserHolder.getTenantId();
        if(StrUtil.isBlank(password)){
            return;
        }
        UpmTenantSystemPO tenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(tenantId,systemId);
        if(ObjUtil.isNull(tenantSystemPO)){
            throw new BusinessException(MessageCode.SYSTEM_TENANT_REL_NOT_EXIST);
        }
        CheckRulePO checkRule = tenantSystemPO.getCheckRule();
        String regular = PasswordCheckEnum.STRONG.getRegular();
        if (ObjUtil.isNotNull(checkRule) && ObjUtil.isNotNull(checkRule.getPasswdCheckType())) {
            regular = PasswordCheckEnum.of(checkRule.getPasswdCheckType()).getRegular();
        }
        if(!password.matches(regular)){
            throw new BusinessException(MessageCode.PASSWORD_CHECK_ERROR);
        }
    }

    public void checkChangeUserInfo(UserInfoChangeCommand command){
        if(StrUtil.isBlank(command.getAvatar()) && StrUtil.isBlank(command.getRemark()) && StrUtil.isBlank(command.getNick()) && StrUtil.isBlank(command.getEmail())){
            throw new CheckException(MessageCode.NO_UPDATE_ERROR);
        }
    }

    public void checkBindPhone(UserBindPhoneCommand command){
        //当前用户没有手机号
        UpmAccountPO upmAccountPO = accountMapper.selectById(CurrentUserHolder.getAccountId());
        if(ObjUtil.isNull(upmAccountPO)){
            throw new CheckException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        if(StrUtil.isNotBlank(upmAccountPO.getTelephone())){
            throw new CheckException(MessageCode.TELEPHONE_BIND_EXIST);
        }
        //校验新手机是否已绑定
        UpmAccountPO accountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(), CurrentUserHolder.getSystemId());
        if(ObjUtil.isNotNull(accountPO)){
            throw new CheckException(MessageCode.TELEPHONE_USER_BIND_EXIST);
        }
        //验证码校验
        SmsUtil.checkTelephone(SmsUtil.BUSINESS_CODE_OF_BINDING,command.getTelephone(),command.getSmsVerificationCode());
    }

    public void checkChangeBindPhone(UserChangeBindPhoneCommand command){
        //当前有手机号,手机号和旧手机一致,新旧手机号不一致,旧手机验证码,新手机验证码
        UpmAccountPO upmAccountPO = accountMapper.selectById(CurrentUserHolder.getAccountId());
        if(ObjUtil.isNull(upmAccountPO)){
            throw new CheckException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        if(StrUtil.isBlank(upmAccountPO.getTelephone())){
            throw new CheckException(MessageCode.TELEPHONE_BIND_NOT_EXIST);
        }
        if(!upmAccountPO.getTelephone().equals(command.getOldTelephone())){
            throw new CheckException(MessageCode.TELEPHONE_BIND_ERROR);
        }
        if(command.getTelephone().equals(command.getOldTelephone())){
            throw new CheckException(MessageCode.TELEPHONE_BIND_EQUAL);
        }
        //校验新手机是否已绑定
        UpmAccountPO accountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(), CurrentUserHolder.getSystemId());
        if(ObjUtil.isNotNull(accountPO)){
            throw new CheckException(MessageCode.TELEPHONE_USER_BIND_EXIST);
        }
        //验证码校验
        SmsUtil.checkTelephone(SmsUtil.BUSINESS_CODE_OF_BINDING,command.getOldTelephone(),command.getOldSmsVerificationCode());
        SmsUtil.checkTelephone(SmsUtil.BUSINESS_CODE_OF_BINDING,command.getTelephone(),command.getSmsVerificationCode());
    }

    public UpmAccountPO checkChangePdByPhone(PdChangeByPhoneCommand command){
        //获取账号,验证手机号是否相同
        Long accountId = Optional.ofNullable(CurrentUserHolder.getCurrentUser()).map(SsoUserAuthInfoDTO::getAccountId).orElse(null);
        Long systemId = Optional.ofNullable(CurrentUserHolder.getCurrentUser()).map(SsoUserAuthInfoDTO::getSystemId).orElse(null);
        String systemCode = Optional.ofNullable(CurrentUserHolder.getCurrentUser()).map(SsoUserAuthInfoDTO::getSystemCode).orElse(null);
        if(ObjUtil.isNotNull(accountId)){
            UpmAccountPO upmAccountPO = accountMapper.selectById(accountId);
            log.info("[checkChangePdByPhone] accountPO:{}", JsonUtils.toJson(upmAccountPO));
            if(ObjUtil.isNull(upmAccountPO)){
                throw new CheckException(MessageCode.USER_NOT_EXIST_ERROR);
            }
            if(!upmAccountPO.getTelephone().equals(command.getTelephone())){
                throw new CheckException(MessageCode.TELEPHONE_BIND_ERROR);
            }
        }
        //短信验证
        SmsUtil.checkTelephone(SmsUtil.BUSINESS_CODE_OF_CHANGE_PASSWORD,command.getTelephone(),command.getSmsVerificationCode());
        //system验证
        if(StrUtil.isBlank(command.getSystemCode()) && ObjUtil.isNull(systemId)){
            throw new CheckException(MessageCode.SYSTEM_CODE_ERROR);
        }
        //systemId冲突
        if (StrUtil.isNotBlank(command.getSystemCode()) && StrUtil.isNotBlank(systemCode)
            && !command.getSystemCode().equals(systemCode)){
            throw new CheckException(MessageCode.SYSTEM_CODE_ERROR);
        }
        if(StrUtil.isNotBlank(command.getSystemCode())){
            UpmSystem upmSystem = systemRepository.find(command.getSystemCode());
            systemId = upmSystem.getId();
        }
        UpmAccountPO accountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(),systemId);
        if(ObjUtil.isNull(accountPO)){
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        //两次密码不相同
        String password = this.decryptPassword(command.getPassword());
        String confirmPassword = this.decryptPassword(command.getConfirmPassword());
        if(!password.equals(confirmPassword)){
            throw new CheckException(MessageCode.PASSWORD_CHECK_FAIL);
        }
        //密码强度
        if(!password.matches(PasswordCheckEnum.STRONG.getRegular())){
            throw new BusinessException(MessageCode.PASSWORD_CHECK_ERROR);
        }
        return accountPO;
    }

    public void checkChangePdByPd(PdChangeByPdCommand command){
        String oldPassword = this.decryptPassword(command.getOldPassword());
        String password = this.decryptPassword(command.getPassword());
        String confirmPassword = this.decryptPassword(command.getConfirmPassword());
        //新旧密码笔记
        if(password.equals(oldPassword)){
            throw new CheckException(MessageCode.PASSWORD_CHECK_FAIL2);
        }
        //新密码和确认密码比较
        if(!password.equals(confirmPassword)){
            throw new CheckException(MessageCode.PASSWORD_CHECK_FAIL);
        }
        //获取账号信息,验证原密码是否相同
        UpmAccountPO upmAccountPO = accountMapper.selectById(CurrentUserHolder.getAccountId());
        if(ObjUtil.isNull(upmAccountPO)){
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        UserAccountInfoDTO accountInfo = new UserAccountInfoDTO()
            .setPassword(upmAccountPO.getPassword())
            .setSalt(upmAccountPO.getSalt());
        if(!ssoAccountActionService.checkPassword(command.getOldPassword(), accountInfo)){
            throw new CheckException(MessageCode.PASSWORD_CHECK_FAIL3);
        }
        //密码强度
        if(!password.matches(PasswordCheckEnum.STRONG.getRegular())){
            throw new BusinessException(MessageCode.PASSWORD_CHECK_ERROR);
        }
    }

    /**
     * 解密密码
     * @param encryption
     * @return
     */
    private String decryptPassword(String encryption){
        String password = null;
        try {
            password = RSAUtil.decryptBase64(encryption);
        } catch (Exception e) {
            log.error("password 解密失败", e);
            throw new CheckException(MessageCode.USER_NOT_EXIST);
        }
        return password;
    }

    public UserAccountInfoDTO getExistUser(String phone, Long systemId) {
        UpmAccountPO upmAccount = accountMapper.selectByTelephoneAndSystemId(phone, systemId);
        if (ObjUtil.isNotNull(upmAccount)) {
            return UserAccountInfoDTO.builder()
                .accountId(upmAccount.getId())
                .username(upmAccount.getUsername())
                .password(upmAccount.getPassword())
                .salt(upmAccount.getSalt())
                .telephone(upmAccount.getTelephone())
                .build();
        }
        return null;
    }

    //通过飞书登录,查找他对应的账号，有可能飞书对应手机号与用户登记的手机好不同
    public UserAccountInfoDTO getExistFsUser(String openId, String appId,Long systemId) {
        UpmAccountPO upmAccount = accountMapper.selectByOpenIdAndAppId(openId,appId,systemId);
        if (ObjUtil.isNotNull(upmAccount)) {
            return UserAccountInfoDTO.builder()
                .accountId(upmAccount.getId())
                .username(upmAccount.getUsername())
                .password(upmAccount.getPassword())
                .salt(upmAccount.getSalt())
                .telephone(upmAccount.getTelephone())
                .build();
        }
        return null;
    }


    public UserAccountInfoDTO getAccountInfoById(Long accountId) {
        UpmAccountPO upmAccount = accountMapper.selectById(accountId);
        return UserAccountInfoDTO.builder()
            .accountId(upmAccount.getId())
            .username(upmAccount.getUsername())
            .password(upmAccount.getPassword())
            .salt(upmAccount.getSalt())
            .telephone(upmAccount.getTelephone())
            .build();
    }
}
