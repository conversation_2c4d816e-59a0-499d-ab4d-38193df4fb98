package cn.genn.trans.upm.application.service.action;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.sso.SaSsoUtil;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.pms.interfaces.api.IOperatorInfoFeignService;
import cn.genn.trans.pms.interfaces.dto.PmsOperatorDTO;
import cn.genn.trans.support.ncs.interfaces.api.INotifyService;
import cn.genn.trans.support.ncs.interfaces.command.MsgRecipientCommand;
import cn.genn.trans.support.ncs.interfaces.command.SystemMsgNotifyCommand;
import cn.genn.trans.support.ncs.interfaces.enums.ChannelType;
import cn.genn.trans.upm.application.assembler.UpmResourceAssembler;
import cn.genn.trans.upm.application.assembler.UpmUserAssembler;
import cn.genn.trans.upm.application.assembler.UpmUserLoginAssembler;
import cn.genn.trans.upm.application.processor.UserProcessor;
import cn.genn.trans.upm.application.service.query.UpmResourceQueryService;
import cn.genn.trans.upm.application.service.query.UpmSystemQueryService;
import cn.genn.trans.upm.application.service.query.UpmTenantQueryService;
import cn.genn.trans.upm.domain.upm.model.entity.*;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDominOfCasbinRuleService;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.constant.CacheConstants;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.infrastructure.utils.*;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.command.CheckVerificationCodeCommand;
import cn.genn.trans.upm.interfaces.command.feishu.UpmFSUserCommand;
import cn.genn.trans.upm.interfaces.dto.*;
import cn.genn.trans.upm.interfaces.dto.feishu.FsDepartmentDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FsExtraInfoDTO;
import cn.genn.trans.upm.interfaces.enums.*;
import cn.genn.trans.upm.interfaces.query.*;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 实现单点登录sso token 登录 续期 登出的相关功能
 *
 * @Date: 2024/4/16
 * @Author: kangjian
 */
@Service
@Slf4j
public class SsoTokenActionService {

    @Resource
    private SsoAccountActionService ssoAccountActionService;
    @Resource
    private ResourceDominOfCasbinRuleService casbinRuleService;
    @Resource
    private UserRepository userRepository;
    @Resource
    private UpmResourceAssembler upmResourceAssembler;
    @Resource
    private SystemDomainService systemDomainService;
    @Resource
    private UpmTenantQueryService upmTenantQueryService;
    @Resource
    private UpmSystemQueryService upmSystemQueryService;
    @Resource
    private UpmResourceQueryService resourceQueryService;
    @Resource
    private SystemRepository systemRepository;
    @Resource
    private INotifyService notifyService;
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private SsoAuthProperties ssoAuthProperties;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UpmSystemMapper upmSystemMapper;
    @Resource
    private UserProcessor userProcessor;
    @Resource
    private UserDomainService userDomainService;
    @Resource
    private UpmUserAssembler userAssembler;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private IOperatorInfoFeignService operatorInfoFeignService;

    public SsoTokenActionService(UpmSeverProperties upmSeverProperties) {
        this.upmSeverProperties = upmSeverProperties;
    }

    /**
     * 登录
     *
     * @return
     */
    public LoginUserAccountDTO ssoAccountDoLogin(SsoAccountLoginQuery query, String vpcGroup) {
        String username = query.getUsername();
        String password = query.getPassword();
        String systemCode = query.getSystemCode();
        // 调用upm进行用户查询
        UserAccountInfoDTO accountInfo = ssoAccountActionService.queryAccountInfoByUserName(username, systemCode);
        if (Objects.isNull(accountInfo)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(accountInfo.getAccountId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(accountInfo.getAccountId()));
        }
        // 验证码校验
        if (!query.isSkipVerificationFlag() && !CaptchaUtil.verification(query.getVerificationCode())) {
            throw new BusinessException(MessageCode.USER_CAPTCHA_FAIL);
        }
        if (!ssoAccountActionService.checkPassword(password, accountInfo)) {
            long failSize = LoginFailUtil.recordLoginFail(String.valueOf(accountInfo.getAccountId()));
            LoginFailUtil.loginFailException(failSize, String.valueOf(accountInfo.getAccountId()));
            // throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(accountInfo.getAccountId()));
        // 查询账号绑定的所有启用用户
        return getLoginUserAccountDTO(accountInfo, systemCode, vpcGroup);
    }


    /**
     * 短信验证码登录
     *
     * @param query
     * @return
     */
    public LoginUserAccountDTO ssoAccountDoSmsLogin(SsoAccountSmsLoginQuery query, String vpcGroup) {
        String systemCode = query.getSystemCode();
        // 调用upm进行用户查询
        UserAccountInfoDTO accountInfo = ssoAccountActionService.queryAccountInfoByTelephone(query.getTelephone(), systemCode);
        if (Objects.isNull(accountInfo)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 验证码校验
        if (!query.isSkipVerificationFlag() && !CaptchaUtil.verification(query.getVerificationCode())) {
            throw new BusinessException(MessageCode.USER_CAPTCHA_FAIL);
        }
        // 校验输入的短信验证码
        SmsUtil.checkLoginTelephone(query.getTelephone(), query.getSmsVerificationCode(), accountInfo.getAccountId());
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(accountInfo.getAccountId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(accountInfo.getAccountId()));
        }
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(accountInfo.getAccountId()));
        return getLoginUserAccountDTO(accountInfo, systemCode, vpcGroup);
    }

    private LoginUserAccountDTO getLoginUserAccountDTO(UserAccountInfoDTO accountInfo, String systemCode, String vpcGroup) {
        // 查询账号绑定的所有启用用户
        List<UpmUser> upmUserList = userRepository.queryListByAccountIdAndVpcGroup(accountInfo.getAccountId(), vpcGroup);
        if (CollUtil.isEmpty(upmUserList)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 生成设备标识和ticket码 ticket码的维度 账号+设备码 防止多个web登录同账号的ticket码冲突
        String userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify(systemCode);
        String loginId = SsoTokenUtil.generateAccountLoginId(accountInfo.getAccountId().toString(), userDeviceIdentify);
        String ticket = SaSsoUtil.createTicket(loginId, userDeviceIdentify);
        log.info("生成的ticket码为: {}", ticket);
        LoginUserAccountDTO loginUserAccount = UpmUserLoginAssembler.INSTANCE.userAccountInfoDTO2LoginUserAccountDTO(accountInfo);
        loginUserAccount.setUserDeviceIdentify(userDeviceIdentify);
        loginUserAccount.setTicket(ticket);
        List<UserAccountBindUserDTO> accountBindUserDTOS = UpmUserLoginAssembler.INSTANCE.upmUserList2UserAccountBindUserDTOList(upmUserList);
        // 过滤只有符合systemCode和用户systemCode相匹配的用户
        populateSystemAndTenantDetail(accountBindUserDTOS);
        accountBindUserDTOS = accountBindUserDTOS.stream().filter(user -> user.getSystemCode().equals(systemCode))
            .collect(Collectors.toList());
        // tms游客用户特殊处理 如果存在游客用户和其他运营方下的用户 则过滤掉
        accountBindUserDTOS = tmsSystemUserFilter(accountBindUserDTOS);
        if (CollectionUtils.isEmpty(accountBindUserDTOS)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        loginUserAccount.setBindUserList(accountBindUserDTOS);
        return loginUserAccount;
    }

    // tms游客用户特殊处理 如果存在游客用户和其他运营方下的用户 则过滤掉
    private List<UserAccountBindUserDTO> tmsSystemUserFilter(List<UserAccountBindUserDTO> accountBindUserDTOS) {
        if (CollectionUtils.isEmpty(accountBindUserDTOS) || accountBindUserDTOS.get(0).getSystemId().equals(ssoAuthProperties.getFspSystemId())) {
            return accountBindUserDTOS;
        }
        if (accountBindUserDTOS.size() == 1) {
            return accountBindUserDTOS;
        }
        // 过滤掉租户是平台类型的用户
        return accountBindUserDTOS.stream()
            .filter(user -> !user.getTenantType().equals(TenantTypeEnum.PLATFORM))
            .collect(Collectors.toList());
    }

    private void populateSystemAndTenantDetail(List<UserAccountBindUserDTO> accountBindUserDTOS) {
        if (CollectionUtils.isEmpty(accountBindUserDTOS)) {
            return;
        }
        List<Long> systemIdList = accountBindUserDTOS.stream().map(UserAccountBindUserDTO::getSystemId).distinct()
            .collect(Collectors.toList());
        List<UpmSystemDTO> upmSystemDTOList = upmSystemQueryService.getByIdList(systemIdList);
        Map<Long, UpmSystemDTO> systemMap = upmSystemDTOList.stream().collect(Collectors.toMap(UpmSystemDTO::getId, dto -> dto));
        List<Long> tenantIdList = accountBindUserDTOS.stream().map(UserAccountBindUserDTO::getTenantId).distinct()
            .collect(Collectors.toList());
        List<UpmTenantDTO> upmTenantDTOList = upmTenantQueryService.getByIdList(tenantIdList);
        Map<Long, UpmTenantDTO> tenantMap = upmTenantDTOList.stream().collect(Collectors.toMap(UpmTenantDTO::getId, dto -> dto));

        accountBindUserDTOS.forEach(user -> {
            UpmSystemDTO upmSystemDTO = systemMap.get(user.getSystemId());
            if (Objects.nonNull(upmSystemDTO)) {
                user.setSystemCode(upmSystemDTO.getCode());
                user.setSystemName(upmSystemDTO.getName());
            }
            UpmTenantDTO upmTenantDTO = tenantMap.get(user.getTenantId());
            if (Objects.nonNull(upmTenantDTO)) {
                user.setTenantName(upmTenantDTO.getName());
                user.setTenantCode(upmTenantDTO.getCode());
                user.setTenantType(upmTenantDTO.getType());
            }
        });
    }

    /**
     * 生成token 分配用于验签的密钥
     *
     * @return
     */
    public UserTokenDTO ssoSystemDoLoginToken(SsoUserLoginQuery query) {
        String systemCode = query.getSystemCode();
        String ticket = query.getTicket();
        Long userId = query.getUserId();
        String userDeviceIdentify = query.getUserDeviceIdentify();
        // 参数校验 扩展支持承运商登录和司机登录
        checkSystemDoLoginParams(query);
        // 查询ticket绑定的账号
        String loginId = SaSsoUtil.getLoginId(ticket, String.class);
        log.info("ticket绑定的loginId tiket={} loginId={}", ticket, loginId);
        if (StrUtil.isBlank(loginId)) {
            throw new BusinessException(MessageCode.USER_TICKET_NOT_EXIST);
        }
        Long accountId = SsoTokenUtil.getAccountIdByLoginId(loginId);
        // 校验账号和userId是否存在关系
        List<UserAccountBindUserDTO> accountBindUsers = ssoAccountActionService.queryUserInfoByAccountId(accountId);
        if (CollectionUtil.isEmpty(accountBindUsers)) {
            throw new BusinessException(MessageCode.USER_UN_BIND_ACCOUNT);
        }

        boolean flag = accountBindUsers.stream().anyMatch(user -> user.getUserId().equals(userId));
        if (!flag) {
            throw new BusinessException(MessageCode.USER_UN_BIND_ACCOUNT);
        }

        // 生成token
        UserTokenDTO userTokenDTO = generateUserToken(systemCode, accountId, userId, userDeviceIdentify, query.getToken());
        log.info("生成的userTokenDTO为: {}", JSONUtil.toJsonStr(userTokenDTO));
        //  查询upm权限当前系统和租户下的角色和权限code缓存在token中 返回token
        UpmUser user = userRepository.findUserRoleResource(userId);
        if(ObjUtil.isNotNull(user.getEffectiveTime()) && user.getEffectiveTime().isBefore(LocalDateTime.now())){
            throw new BusinessException(MessageCode.USER_EXPIRED);
        }
        LoginUserAuthInfoDTO loginUserAuthInfoDTO = generateTokenSessionData(query, accountId, user, userTokenDTO);
        log.info("loginUserAuthInfoDTO:{}", JsonUtils.toJson(loginUserAuthInfoDTO));
        loginUserAuthInfoDTO.setToken(userTokenDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(userTokenDTO.getToken(), loginUserAuthInfoDTO);
        userTokenDTO.setVpcGroup(loginUserAuthInfoDTO.getVpcGroup());
        return userTokenDTO;
    }

    private void checkSystemDoLoginParams(SsoUserLoginQuery query) {
        UpmSystem upmSystem = systemRepository.find(query.getSystemCode());
        if (Objects.isNull(upmSystem)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }
        switch (upmSystem.getType()) {
            case OPERATOR:
                Assert.notNull(query.getOperatorId(), "运营商登录时运营商id不能为空");
                Assert.notBlank(query.getOperatorName(), "运营商登录时运营商名称不能为空");
                break;
            case CARRIER:
                // 查询当前用户绑定的租户类型 如果是平台 说明是游客 无需传承运商id和name
                UpmUserPO upmUserPO = userRepository.selectById(query.getUserId());
                if (Objects.isNull(upmUserPO)) {
                    throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
                }
                UpmTenantDTO upmTenantDTO = upmTenantQueryService.get(upmUserPO.getTenantId());
                if (Objects.isNull(upmTenantDTO)) {
                    throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
                }
                if (!TenantTypeEnum.PLATFORM.equals(upmTenantDTO.getType())) {
                    Assert.notNull(query.getCarrierId(), "承运商登录时承运商id不能为空");
                    Assert.notBlank(query.getCarrierName(), "承运商登录时承运商名称不能为空");
                }
                break;
            case STATION:
                Assert.notNull(query.getStationId(), "站端登录时站点id不能为空");
                Assert.notBlank(query.getStationName(), "站端登录时站点名称不能为空");
                break;
            case PLATFORM:
            default:
        }
    }

    private LoginUserAuthInfoDTO generateTokenSessionData(SsoUserLoginQuery query, Long accountId, UpmUser user, UserTokenDTO userTokenDTO) {
        LoginUserAuthInfoDTO loginUserAuthInfoDTO = new LoginUserAuthInfoDTO();
        loginUserAuthInfoDTO.setAccountId(accountId);
        loginUserAuthInfoDTO.setUserId(query.getUserId());
        loginUserAuthInfoDTO.setSecretKey(userTokenDTO.getSecretKey());
        loginUserAuthInfoDTO.setTicket(query.getTicket());
        loginUserAuthInfoDTO.setUserDeviceIdentify(query.getUserDeviceIdentify());
        if (Objects.nonNull(user)) {
            loginUserAuthInfoDTO.setEmail(user.getEmail());
            loginUserAuthInfoDTO.setAvatar(user.getAvatar());
            loginUserAuthInfoDTO.setNick(user.getNick());
            loginUserAuthInfoDTO.setRemark(user.getRemark());
            UpmAccount upmAccount = userRepository.queryAccountById(accountId);
            loginUserAuthInfoDTO.setUsername(Optional.ofNullable(upmAccount).map(UpmAccount::getUsername).orElse(""));
            loginUserAuthInfoDTO.setRoles(Optional.ofNullable(user.getRoleList()).orElse(Lists.newArrayList()).stream()
                .map(UpmRole::getCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            loginUserAuthInfoDTO.setPermissions(Optional.ofNullable(user.getRoleList()).orElse(Lists.newArrayList()).stream()
                .map(UpmRole::getResourceList)
                .flatMap(List::stream)
                .map(UpmResource::getCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet()));
            // 手机号先获取account的 不存在取user中的
            String telephone = Optional.ofNullable(upmAccount).map(UpmAccount::getTelephone).orElse("");
            if (StrUtil.isBlank(telephone)) {
                telephone = Optional.of(user).map(UpmUser::getTelephone).orElse("");
            }
            loginUserAuthInfoDTO.setTelephone(telephone);
            loginUserAuthInfoDTO.setPasswordStatus(Optional.ofNullable(upmAccount).map(UpmAccount::getPasswordStatus).orElse(PasswrodStatusEnum.NORMAL));
            loginUserAuthInfoDTO.setPdUpdateTime(Optional.ofNullable(upmAccount).map(UpmAccount::getPdUpdateTime).orElse(null));
            loginUserAuthInfoDTO.setSystemId(user.getSystemId());
            if(ObjUtil.isNotNull(upmAccount) && StrUtil.isNotBlank(upmAccount.getCompanyName())){
                loginUserAuthInfoDTO.setCompanyName(upmAccount.getCompanyName());
            }
            if(ObjUtil.isNotNull(upmAccount) && StrUtil.isNotBlank(upmAccount.getSaleName())){
                loginUserAuthInfoDTO.setSaleName(upmAccount.getSaleName());
            }
            if(ObjUtil.isNotNull(user.getEffectiveTime())){
                loginUserAuthInfoDTO.setEffectiveTime(user.getEffectiveTime());
            }
            UpmSystem upmSystem = systemDomainService.find(user.getSystemId());
            loginUserAuthInfoDTO.setSystemCode(Optional.ofNullable(upmSystem).map(UpmSystem::getCode).orElse(""));
            loginUserAuthInfoDTO.setSystemName(Optional.ofNullable(upmSystem).map(UpmSystem::getName).orElse(""));

            loginUserAuthInfoDTO.setTenantId(user.getTenantId());
            UpmTenantDTO upmTenantDTO = upmTenantQueryService.get(user.getTenantId());
            loginUserAuthInfoDTO.setTenantName(Optional.ofNullable(upmTenantDTO).map(UpmTenantDTO::getName).orElse(""));
            loginUserAuthInfoDTO.setTenantCode(Optional.ofNullable(upmTenantDTO).map(UpmTenantDTO::getCode).orElse(""));
            loginUserAuthInfoDTO.setVpcGroup(Optional.ofNullable(upmTenantDTO).map(UpmTenantDTO::getVpcGroup).orElse("default"));
            loginUserAuthInfoDTO.setAuthKey(user.getAuthKey());
            loginUserAuthInfoDTO.setAuthGroup(AuthKeyUtil.getAuthGroupEnum(user.getAuthKey()));
        }
        UpmSystem upmSystem = systemRepository.find(query.getSystemCode());
        if (Objects.nonNull(upmSystem)) {
            loginUserAuthInfoDTO.setSystemType(upmSystem.getType());
            switch (upmSystem.getType()) {
                case FLEET:
                    loginUserAuthInfoDTO.setOperatorId(query.getOperatorId());
                    loginUserAuthInfoDTO.setOperatorName(query.getOperatorName());
                    loginUserAuthInfoDTO.setOperatorCode(query.getOperatorCode());
                    loginUserAuthInfoDTO.setStationId(query.getStationId());
                    loginUserAuthInfoDTO.setStationName(query.getStationName());
                    loginUserAuthInfoDTO.setMemberId(query.getMemberId());
                    loginUserAuthInfoDTO.setMemberNo(query.getMemberNo());
                    break;
                case OPERATOR:
                    loginUserAuthInfoDTO.setOperatorId(query.getOperatorId());
                    loginUserAuthInfoDTO.setOperatorName(query.getOperatorName());
                    loginUserAuthInfoDTO.setOperatorCode(query.getOperatorCode());
                    PmsOperatorDTO operatorDTO = operatorInfoFeignService.get(query.getOperatorId());
                    loginUserAuthInfoDTO.setOperatorType(Optional.ofNullable(operatorDTO).orElse(new PmsOperatorDTO()).getOperatorType().getType());

                    break;
                case CARRIER:
                    loginUserAuthInfoDTO.setCarrierId(query.getCarrierId());
                    loginUserAuthInfoDTO.setCarrierName(query.getCarrierName());
                    loginUserAuthInfoDTO.setOperatorCarrierRelId(query.getOperatorCarrierRelId());
                    loginUserAuthInfoDTO.setOperatorId(query.getOperatorId());
                    loginUserAuthInfoDTO.setOperatorName(query.getOperatorName());
                    loginUserAuthInfoDTO.setOperatorCode(query.getOperatorCode());
                    if (ObjUtil.isNotNull(query.getOperatorId())) {
                        PmsOperatorDTO carrierOperatorDTO = operatorInfoFeignService.get(query.getOperatorId());
                        loginUserAuthInfoDTO.setOperatorType(Optional.ofNullable(carrierOperatorDTO).orElse(new PmsOperatorDTO()).getOperatorType().getType());
                    }
                    break;
                case STATION:
                    loginUserAuthInfoDTO.setStationId(query.getStationId());
                    loginUserAuthInfoDTO.setStationName(query.getStationName());
                    loginUserAuthInfoDTO.setOperatorId(query.getOperatorId());
                    loginUserAuthInfoDTO.setOperatorName(query.getOperatorName());
                    loginUserAuthInfoDTO.setOperatorCode(query.getOperatorCode());
                    break;
                case PLATFORM:
                default:
            }
        }
        // 保理公司处理
        if (user.getSystemId().equals(ssoAuthProperties.getFspSystemId())) {
            loginUserAuthInfoDTO.setCompanyId(AuthKeyUtil.getOriginId(user.getAuthKey()));
        }

        UpmUserThirdPO thirdInfo = userThirdMapper.selectFSByUserId(user.getId());
        if (thirdInfo != null) {
            loginUserAuthInfoDTO.setFsAppId(thirdInfo.getAppId());
            loginUserAuthInfoDTO.setFsOpenId(thirdInfo.getOpenId());
            String extraInfo = thirdInfo.getExtraInfo();
            if (StrUtil.isNotBlank(extraInfo)) {
                FsExtraInfoDTO fsExtraInfoDTO = JsonUtils.parse(extraInfo, FsExtraInfoDTO.class);
                if (ObjUtil.isNotNull(fsExtraInfoDTO)) {
                    if (CollUtil.isNotEmpty(fsExtraInfoDTO.getDepartments())) {
                        loginUserAuthInfoDTO.setFsDepartments(fsExtraInfoDTO.getDepartments());
                    }
                    if (StrUtil.isNotBlank(fsExtraInfoDTO.getAvatarUrl())){
                        loginUserAuthInfoDTO.setFsAvatarUrl(fsExtraInfoDTO.getAvatarUrl());
                    }
                    }
                }
            }


            return loginUserAuthInfoDTO;
        }

        /**
         * 获取token中绑定的用户信息
         *
         * @param token
         * @return
         */
        public LoginUserAuthInfoDTO ssoSystemUserInfo (String token){
            LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
            if (Objects.isNull(authInfoDTO)) {
                throw new BusinessException(MessageCode.USER_NOT_LOGIN);
            }
            List<UpmResourcePO> upmResourcePOS = resourceQueryService.userQueryList(authInfoDTO.getAuthKey(), authInfoDTO.getUserId(), authInfoDTO.getSystemId(), authInfoDTO.getTenantId());
            if (CollUtil.isNotEmpty(upmResourcePOS)) {
                authInfoDTO.setResourceTree(upmResourceAssembler.PO2TREEDTO(upmResourcePOS));
                authInfoDTO.setPermissions(upmResourcePOS.stream().map(UpmResourcePO::getPath).collect(Collectors.toSet()));
            }
            return authInfoDTO;
        }

        /**
         * 退出登录
         *
         * @param token
         * @param userDeviceIdentify
         */
        public void ssoAccountLogout (String token, String userDeviceIdentify){
            // 查找账号同一设备下的所有的token
            LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
            if (Objects.isNull(authInfoDTO)) {
                return;
            }
            Long accountId = authInfoDTO.getAccountId();
            if (Objects.isNull(accountId)) {
                return;
            }
            // 调用upm查询accountId绑定的所有用户
            List<UserAccountBindUserDTO> accountBindUsers = ssoAccountActionService.queryUserInfoByAccountId(accountId);
            // 所有账号绑定的user token退出
            accountBindUsers.forEach(user -> StpUtil.logout(SsoTokenUtil.generateUserLoginId(accountId, user.getUserId()), userDeviceIdentify));
        }

        /**
         * 退出账号下所有用户
         *
         * @param accountId
         */
        public void ssoAccountLogout (Long accountId){
            // 调用upm查询accountId绑定的所有用户
            List<UserAccountBindUserDTO> accountBindUsers = ssoAccountActionService.queryUserInfoByAccountId(accountId);
            // 所有账号绑定的user token退出
            accountBindUsers.forEach(user -> StpUtil.logout(SsoTokenUtil.generateUserLoginId(accountId, user.getUserId())));
        }


        /**
         * 按用户把所有token失效
         */
        public void logoutByUserIdList (List < Long > userIds) {
            // 查找账号同一设备下的所有的token
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            List<UpmUserPO> userList = userRepository.selectByIds(userIds);
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            userList.forEach(user -> {
                Long accountId = user.getAccountId();
                if (Objects.isNull(accountId)) {
                    return;
                }
                StpUtil.logout(SsoTokenUtil.generateUserLoginId(accountId, user.getId()));
            });
        }

        public void logoutByAuthKey (String authKey){
            UpmUserQuery query = new UpmUserQuery();
            query.setAuthKey(authKey);
            query.setDeleted(DeletedEnum.NOT_DELETED);
            List<UpmUserDTO> userDTOS = upmUserMapper.selectByQuery(query);
            if (CollUtil.isNotEmpty(userDTOS)) {
                for (UpmUserDTO userDTO : userDTOS) {
                    StpUtil.logout(SsoTokenUtil.generateUserLoginId(userDTO.getAccountId(), userDTO.getId()));
                }
            }
        }

        public void logoutBySystemIds (List < Long > systemIds) {
            if (CollectionUtils.isEmpty(systemIds)) {
                return;
            }
            List<UpmUserPO> userPOs = upmUserMapper.selectBySystemIds(systemIds);
            if (CollUtil.isNotEmpty(userPOs)) {
                for (UpmUserPO userPO : userPOs) {
                    StpUtil.logout(SsoTokenUtil.generateUserLoginId(userPO.getAccountId(), userPO.getId()));
                }
            }
        }

        public void logoutBySystemType (SystemTypeEnum typeEnum){
            List<UpmSystemPO> upmSystemPOS = upmSystemMapper.selectByType(typeEnum);
            if (CollUtil.isNotEmpty(upmSystemPOS)) {
                List<Long> systemIds = upmSystemPOS.stream().map(UpmSystemPO::getId).collect(Collectors.toList());
                this.logoutBySystemIds(systemIds);
            }
        }

        /**
         * token续约
         */
        public void refreshToken (String token){
            StpUtil.getStpLogic().updateLastActiveToNow(token);
        }

        /**
         * 根据用户设备生成token
         *
         * @param accountId
         */
        public UserTokenDTO generateUserToken (String systemCode, Long accountId, Long userId, String
        userDeviceIdentify, String preToken){
            /**
             * 通过username和password查询upm用户信息
             * 如果用户存在 按设备唯一标识给用户生成一个token
             * 查询upm用户绑定的权限信息
             * 将用户相关信息存储在token缓存中
             */
            if (StrUtil.isBlank(userDeviceIdentify)) {
                userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify();
            }
            SaLoginModel saLoginModel = buildSaLoginModel(systemCode, userDeviceIdentify, preToken);

            // 如果不允许并发登录 注销掉token
            if (systemSingleLoginFlag(systemCode)) {
                StpUtil.logout(SsoTokenUtil.generateUserLoginId(accountId, userId));
            }
            StpUtil.login(SsoTokenUtil.generateUserLoginId(accountId, userId), saLoginModel);
            String token = StpUtil.getTokenValue();

            if (StrUtil.isBlank(token)) {
                throw new BusinessException(MessageCode.USER_GENERATE_TOKEN_FAIL);
            }
            // 增加给前端返回密钥 分配用于验签的密钥 生成64位的密钥
            String secretKey = RandomUtil.randomString(32);
            return UserTokenDTO.builder()
                .token(token)
                .timeout(saLoginModel.getTimeout())
                .activeTimeout(saLoginModel.getActiveTimeout())
                .secretKey(secretKey)
                .userId(userId)
                .build();
        }

        /**
         * 系统单点登录判断
         *
         * @param systemCode 系统代码
         * @return 是否需要单点登录（true：需要单点登录；false：无需单点登录）
         */
        private boolean systemSingleLoginFlag (String systemCode){

            // 获取非单点登录配置
            UpmSeverProperties.LoginProperties login = upmSeverProperties.getLogin();
            boolean enableNonSingleSignOnConfig = login.isEnableNonSingleSignOnConfig();
            List<String> nonSingleSignOnSystemCodeList = login.getNonSingleSignOnSystemCodeList();
            if (enableNonSingleSignOnConfig && nonSingleSignOnSystemCodeList.contains(systemCode)) {
                // 如果系统在无需单点登录的列表中，则无需单点登录
                return false;
            }

            // sa-token 全局配置：是否允许并发登录（true：允许；false：不允许）
            return Boolean.FALSE.equals(SaManager.getConfig().getIsConcurrent());
        }

        /**
         * 构建saLoginModel
         * TODO:待定:后续可以根据不同的平台端设置不同的过期时间
         */
        public SaLoginModel buildSaLoginModel (String systemCode, String userDeviceIdentify, String preToken){
            SaLoginModel saLoginModel = new SaLoginModel();
            // 允许多客户端同时登录同一账号 默认是允许的、同账号登录不同设备 需要把其他设备下账号踢出
            if (Boolean.TRUE.equals(SaManager.getConfig().getIsConcurrent())) {
                saLoginModel.setDevice(userDeviceIdentify);
            } else {
                saLoginModel.setDevice(systemCode);
            }
            saLoginModel.setTimeout(SsoTokenUtil.getTokenTimeout(systemCode));
            saLoginModel.setActiveTimeout(SsoTokenUtil.getTokenActiveTimeOut(systemCode));
            if (StrUtil.isNotBlank(preToken)) {
                saLoginModel.setToken(preToken);
            }
            return saLoginModel;
        }

        /**
         * 小程序强制单点登录,否则有消息通知bug
         *
         * @param systemCode
         * @return
         */
        public SaLoginModel buildMiniSaLoginModel (String systemCode){
            SaLoginModel saLoginModel = new SaLoginModel();
            saLoginModel.setDevice(systemCode);
            saLoginModel.setTimeout(SsoTokenUtil.getMiniTokenTimeout(systemCode));
            // saLoginModel.setActiveTimeout(SsoTokenUtil.getTokenActiveTimeOut(systemCode));
            return saLoginModel;
        }

        public SaLoginModel buildOfficialSaLoginModel (String systemCode){
            SaLoginModel saLoginModel = new SaLoginModel();
            saLoginModel.setDevice(systemCode);
            saLoginModel.setTimeout(SsoTokenUtil.getOfficialTokenTimeout(systemCode));
            return saLoginModel;
        }

        public Boolean resetAccountCache (Long accountId){
            // 获取当前账号登录的所有token,全部更新缓存;
            List<UpmUserPO> upmUserPOList = upmUserMapper.selectByAccountId(accountId);
            if (CollUtil.isEmpty(upmUserPOList)) {
                return true;
            }
            List<String> tokens = new ArrayList<>();
            for (UpmUserPO upmUserPO : upmUserPOList) {
                List<String> tokenList = StpUtil.getTokenValueListByLoginId(SsoTokenUtil.generateUserLoginId(accountId, upmUserPO.getId()));
                if (CollUtil.isNotEmpty(tokenList)) {
                    tokens.addAll(tokenList);
                }
            }
            tokens.forEach(token -> {
                LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
                if (ObjUtil.isNull(authInfoDTO)) {
                    return;
                }
                if (authInfoDTO.getSystemType() == null || authInfoDTO.getSystemType().equals(SystemTypeEnum.DRIVER)
                    || authInfoDTO.getSystemType().equals(SystemTypeEnum.FLEET) || authInfoDTO.getSystemType().equals(SystemTypeEnum.STATION)) {
                    return;
                }
                SsoUserLoginQuery query = this.toLoginQuery(authInfoDTO);
                UpmUser user = userRepository.findUserRoleResource(authInfoDTO.getUserId());
                UserTokenDTO userTokenDTO = new UserTokenDTO().setSecretKey(authInfoDTO.getSecretKey());
                LoginUserAuthInfoDTO loginUserAuthInfoDTO = this.generateTokenSessionData(query, accountId, user, userTokenDTO);
                loginUserAuthInfoDTO.setToken(token);
                SsoTokenUtil.setLoginUserAuthInfo(token, loginUserAuthInfoDTO);
            });
            return true;
        }

        private SsoUserLoginQuery toLoginQuery (LoginUserAuthInfoDTO authInfoDTO){
            return new SsoUserLoginQuery().builder()
                .userId(authInfoDTO.getUserId())
                .ticket(authInfoDTO.getTicket())
                .userDeviceIdentify(authInfoDTO.getUserDeviceIdentify())
                .systemCode(authInfoDTO.getSystemCode())
                .operatorId(authInfoDTO.getOperatorId())
                .operatorCode(authInfoDTO.getOperatorCode())
                .operatorName(authInfoDTO.getOperatorName())
                .carrierId(authInfoDTO.getCarrierId())
                .carrierName(authInfoDTO.getCarrierName())
                .operatorCarrierRelId(authInfoDTO.getOperatorCarrierRelId())
                .build();
        }

        /**
         * 校验用户是否有权限访问uri
         *
         * @param query
         * @return
         */
        @Cache(value = CacheConstants.CHECK_URI_PERMISSION, fieldKey = "#query.userId + '_' + #query.uri", expireTime = 600)
        public Boolean checkUriPermission (UpmCheckUriPermissionQuery query){
            // 查询用户的租户和系统信息
            UpmUserPO user = userRepository.selectById(query.getUserId());
            if (Objects.isNull(user)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
            }
            return casbinRuleService.checkPermission(user, query.getUri());
        }

        public Boolean sendSms (String telephone, String businessCode){
            String cacheLoginSmsCount = CacheConstants.CACHE_LOGIN_SMS_COUNT + DateUtil.today() + "_" + businessCode + "_" + telephone;
            if (RedisUtils.hasKey(cacheLoginSmsCount)) {
                Long count = RedisUtils.incr(cacheLoginSmsCount, 1L);
                if (count > 30) {
                    throw new BusinessException(MessageCode.USER_SEND_SMS_FAIL);
                }
            } else {
                RedisUtils.set(cacheLoginSmsCount, "1", 36L, TimeUnit.HOURS);
            }
            // 判断距离上次发送验证码是否超过一分钟
            SmsUtil.isSendSmsFrequently(businessCode, telephone);

            Map<String, Object> parameters = new HashMap<>();
            // 生成一个随机的6位数字 通过环境配置 开发环境从配置文件读取
            String code = "";
            if (StrUtil.isNotBlank(upmSeverProperties.getDefaultSmsVerificationCode())) {
                code = upmSeverProperties.getDefaultSmsVerificationCode();
            } else {
                code = RandomUtil.randomNumbers(6);
            }
            // 缓存发送的短信验证码 和 当天发送次数 发送验证码时间
            String cacheLoginSmsKey = SmsUtil.generateCacheNotifyCodeKey(businessCode, telephone);
            RedisUtils.set(cacheLoginSmsKey, code, upmSeverProperties.getSmsEffectiveTime(), TimeUnit.MINUTES);
            String lastActiveSendTimeKey = SmsUtil.generateLastActiveSendTimeKey(businessCode, telephone);
            RedisUtils.set(lastActiveSendTimeKey, String.valueOf(System.currentTimeMillis()), upmSeverProperties.getSmsEffectiveTime(), TimeUnit.MINUTES);

            SystemMsgNotifyCommand command = new SystemMsgNotifyCommand();
            command.setCode(businessCode);
            command.setChannelTypes(Arrays.asList(ChannelType.SMS));
            parameters.put("code", code);
            command.setParams(parameters);
            MsgRecipientCommand recipient = new MsgRecipientCommand();
            recipient.setRecipientId(telephone);
            command.setRecipients(Arrays.asList(recipient));
            notifyService.sendSystemMsg(command);
            return true;
        }


        public String generateTokenByUserId (Long userId){
            // 如果存在当前用户现有的token直接拿来用  不存在生成一个
            UpmUser user = userRepository.findUserRoleResource(userId);
            if (Objects.isNull(user)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
            }
            String loginId = SsoTokenUtil.generateUserLoginId(user.getAccountId(), user.getId());
            String token = StpUtil.getStpLogic().getTokenValueByLoginId(loginId);
            if (StrUtil.isNotBlank(token)) {
                return token;
            }
            UpmSystem upmSystem = systemRepository.find(user.getSystemId());
            UserTokenDTO userToken = generateUserToken(upmSystem.getCode(), user.getAccountId(), user.getId(), null, null);
            SsoUserLoginQuery query = SsoUserLoginQuery.builder()
                .userId(user.getId())
                .build();
            LoginUserAuthInfoDTO loginUserAuthInfoDTO = generateTokenSessionData(query, user.getAccountId(), user, userToken);
            loginUserAuthInfoDTO.setToken(userToken.getToken());
            SsoTokenUtil.setLoginUserAuthInfo(userToken.getToken(), loginUserAuthInfoDTO);
            userToken.setVpcGroup(loginUserAuthInfoDTO.getVpcGroup());
            return userToken.getToken();
        }

        public void checkVerificationCode (CheckVerificationCodeCommand command){
            SmsUtil.checkTelephone(command.getBusinessCode(), command.getTelephone(), command.getSmsVerificationCode());
        }

    /**
     * 新增飞书用户
     *
     * @return Long
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginUserAccountDTO getLoginUserAccount(UpmFSUserCommand command) {
        UpmSystem upmSystem = systemRepository.find(command.getSystemCode());
        UserAccountInfoDTO accountInfo = userProcessor.getExistUser(command.getTelephone(), upmSystem.getId());
        if (accountInfo == null) {
            String authKey = AuthKeyUtil.getCereroKey(upmSystem.getId(), command.getTenantId());
            UpmUser upmUser = userAssembler.fsUserCommand2UpmUser(command);
            upmUser.setTenantId(command.getTenantId());
            upmUser.setSystemId(upmSystem.getId());
            upmUser.setAuthKey(authKey);
            UpmUserPO tenantUser = userDomainService.createTenantUser(upmUser);
            accountInfo = userProcessor.getAccountInfoById(tenantUser.getAccountId());
        }
        checkAndCreateFSThirdInfo(accountInfo.getAccountId(), upmSystem.getId(), command.getAppId(), command.getOpenId(),command.getAvatarUrl(),command.getFsDepartments());
        return getLoginUserAccountDTO(accountInfo, command.getSystemCode(), null);
    }

    @Transactional(rollbackFor = Exception.class)
    public LoginUserAccountDTO getFsLoginUserAccount(UpmFSUserCommand command) {
        UpmSystem upmSystem = systemRepository.find(command.getSystemCode());
        UserAccountInfoDTO accountInfo = userProcessor.getExistFsUser(command.getOpenId(), command.getAppId(),upmSystem.getId());
        if (accountInfo == null) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        return getLoginUserAccountDTO(accountInfo, command.getSystemCode(), null);
    }

        private void checkAndCreateFSThirdInfo (Long accountId, Long systemId, String appId, String openId, String
        avatarUrl, List < FsDepartmentDTO > fsDepartments){
            UpmUserPO userInfo = upmUserMapper.selectByAccountIdAndSystemId(accountId, systemId);
            List<UpmUserThirdPO> thirdInfos = userThirdMapper.selectByUserIdAndType(userInfo.getId(), ThridTypeEnum.FS);
            boolean needCreate = true;
            UpmUserThirdPO updatePO = null; //有数据表示要更新
            // 一个用户关联 启用一个飞书应用
            if (CollUtil.isNotEmpty(thirdInfos)) {
                for (UpmUserThirdPO thirdInfo : thirdInfos) {
                    // 保证只有一个 appId启用
                    if (StringUtils.equals(thirdInfo.getAppId(), appId)) {
                        needCreate = false;
                        if (thirdInfo.getUpdateTime().isBefore(LocalDateTime.now().minusDays(1)) ||
                            (StrUtil.isBlank(thirdInfo.getExtraInfo()) && (CollUtil.isNotEmpty(fsDepartments) || StrUtil.isNotBlank(avatarUrl)))) {
                            updatePO = thirdInfo;
                        }
                        if (!StatusEnum.ENABLE.equals(thirdInfo.getStatus())) {
                            // 一样且未启用 启用
                            userThirdMapper.openByAppIdAndUserId(appId, userInfo.getId());
                        }
                    } else {
                        // 不一样 停用
                        userThirdMapper.closeStatusByUserIdAndAppId(userInfo.getId(), thirdInfo.getAppId());
                    }
                }
            }
            if (needCreate) {
                // 创建飞书三方信息
                FsExtraInfoDTO fsExtraInfoDTO = new FsExtraInfoDTO().setDepartments(fsDepartments).setAvatarUrl(avatarUrl);
                userThirdMapper.createFSThirdInfo(userInfo.getId(), appId, openId, JsonUtils.toJson(fsExtraInfoDTO));
            }
            if (ObjUtil.isNotNull(updatePO)) {
                //更新部门信息
                FsExtraInfoDTO fsExtraInfoDTO = new FsExtraInfoDTO();
                String extraInfo = updatePO.getExtraInfo();
                if (StrUtil.isNotBlank(extraInfo)) {
                    fsExtraInfoDTO = JsonUtils.parse(extraInfo, FsExtraInfoDTO.class);
                }
                fsExtraInfoDTO.setDepartments(fsDepartments);
                fsExtraInfoDTO.setAvatarUrl(avatarUrl);
                userThirdMapper.updateExtraInfoById(updatePO.getId(), JsonUtils.toJson(fsExtraInfoDTO));
            }
        }

    }
