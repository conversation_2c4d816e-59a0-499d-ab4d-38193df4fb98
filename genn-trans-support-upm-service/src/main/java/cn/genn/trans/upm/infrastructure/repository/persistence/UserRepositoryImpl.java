package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmUserConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class UserRepositoryImpl extends ServiceImpl<UpmUserMapper, UpmUserPO> implements UserRepository {

    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UpmAccountMapper upmAccountMapper;
    @Resource
    private UpmUserConverter upmUserConverter;


    @Override
    public UpmAccount queryAccountByUserName(String userName,Long systemId) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmAccountPO>()
            .eq("username", userName)
            .eq("system_id",systemId)
            .eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmAccountPO> poList = upmAccountMapper.selectList(queryWrapper);
        Optional<UpmAccountPO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmAccount.fromPo(optional.get());
    }

    @Override
    public UpmAccount queryAccountByTelephone(String telephone,Long systemId) {
        QueryWrapper<UpmAccountPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(UpmAccountPO::getTelephone, telephone)
            .eq(UpmAccountPO::getSystemId,systemId)
            .eq(UpmAccountPO::getDeleted, DeletedEnum.NOT_DELETED);
        List<UpmAccountPO> poList = upmAccountMapper.selectList(queryWrapper);
        Optional<UpmAccountPO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmAccount.fromPo(optional.get());
    }

    /**
     * 查询 账号根据用户名
     *
     * @param accountId
     * @return
     */
    @Override
    public UpmAccount queryAccountById(Long accountId) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmAccountPO>()
            .eq("id", accountId)
            .eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmAccountPO> poList = upmAccountMapper.selectList(queryWrapper);
        Optional<UpmAccountPO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmAccount.fromPo(optional.get());
    }

    /**
     * 查询已启用用户
     *
     * @param accountId
     * @return
     */
    @Override
    public List<UpmUser> queryListByAccountId(Long accountId) {
        LambdaQueryWrapper<UpmUserPO> queryWrapper = new LambdaQueryWrapper<>(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId, accountId)
            .eq(UpmUserPO::getStatus, StatusEnum.ENABLE.getCode());
        List<UpmUserPO> poList = this.list(queryWrapper);
        return poList.stream().map(UpmUser::fromPo).collect(Collectors.toList());
    }

    /**
     * 查询accountId和vpcGroup下用户
     *
     * @param accountId
     * @return
     */
    @Override
    public List<UpmUser> queryListByAccountIdAndVpcGroup(Long accountId, String vpcGroup) {
        List<UpmUserPO> upmUserList = upmUserMapper.selectByAccountIdAndVpcGroup(accountId,vpcGroup);
        return upmUserList.stream().map(UpmUser::fromPo).collect(Collectors.toList());
    }

    @Override
    public UpmUser findUserRoleResource(Long userId) {
        return upmUserMapper.findUserRoleResource(userId);
    }

    @Override
    public List<UpmUser> findAllUserAndRole() {
        return upmUserMapper.findAllUserAndRole();
    }

    @Override
    public List<UpmUserPO> selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .eq(UpmUserPO::getTenantId, tenantId);
        return baseMapper.selectList(wrapper);
    }

    /**
     * id查询用户
     *
     * @param id
     */
    @Override
    public UpmUserPO selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<UpmUserPO> selectByIds(List<Long> ids) {
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .in(UpmUserPO::getId, ids);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Boolean update(UpmUserPO upmUserPO,Boolean effectiveTimeEnable) {
        LambdaUpdateWrapper<UpmUserPO> userWrapper = Wrappers.lambdaUpdate(UpmUserPO.class)
            .eq(UpmUserPO::getId, upmUserPO.getId())
            .set(Objects.nonNull(upmUserPO.getStatus()), UpmUserPO::getStatus, upmUserPO.getStatus())
            .set(StringUtils.isNotBlank(upmUserPO.getNick()), UpmUserPO::getNick, upmUserPO.getNick())
            .set(ObjUtil.isNotNull(upmUserPO.getEmail()), UpmUserPO::getEmail, upmUserPO.getEmail())
            .set(UpmUserPO::getTelephone, upmUserPO.getTelephone());

        if(!(effectiveTimeEnable && ObjUtil.isNull(upmUserPO.getEffectiveTime()))){
            userWrapper.set(UpmUserPO::getEffectiveTime, upmUserPO.getEffectiveTime());
        }
        upmUserMapper.update(userWrapper);
        return true;
    }

    /**
     * 批量修改状态
     *
     * @param idList
     * @param status
     */
    @Override
    public Long updateStatus(List<Long> idList, StatusEnum status) {
        Long userId = CurrentUserHolder.getUserId();
        String userName = CurrentUserHolder.getUserName();
        LambdaUpdateWrapper<UpmUserPO> userWrapper = Wrappers.lambdaUpdate(UpmUserPO.class)
            .in(UpmUserPO::getId, idList)
            .set(UpmUserPO::getStatus, status)
            .set(UpmUserPO::getUpdateUserId, userId)
            .set(UpmUserPO::getUpdateUserName, userName);
        return (long) baseMapper.update(userWrapper);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    public Boolean batchDelete(List<Long> idList) {
        baseMapper.deleteBatchIds(idList);
        return true;
    }

    @Override
    public boolean saveBatchList(List<UpmUserPO> list) {
        baseMapper.saveBatch(list);
        return true;
    }

    @Override
    public boolean saveBatchPOList(List<UpmUserPO> list) {
        saveBatch(list);
        return true;
    }

    @Override
    public Boolean changeUserInfo(UpmUser upmUser) {
        LambdaUpdateWrapper<UpmUserPO> wrapper = Wrappers.lambdaUpdate(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId, upmUser.getAccountId())
            .set(StrUtil.isNotBlank(upmUser.getNick()), UpmUserPO::getNick, upmUser.getNick())
            .set(StrUtil.isNotBlank(upmUser.getAvatar()), UpmUserPO::getAvatar, upmUser.getAvatar())
            .set(StrUtil.isNotBlank(upmUser.getEmail()), UpmUserPO::getEmail, upmUser.getEmail())
            .set(upmUser.getRemark() !=null,UpmUserPO::getRemark, upmUser.getRemark());
        return update(wrapper);
    }

    @Override
    public Boolean bindPhone(UpmUser upmUser) {
        update(Wrappers.lambdaUpdate(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId, upmUser.getAccountId())
            .set(UpmUserPO::getTelephone, upmUser.getTelephone()));

        upmAccountMapper.update(Wrappers.lambdaUpdate(UpmAccountPO.class)
            .eq(UpmAccountPO::getId, upmUser.getAccountId())
            .set(UpmAccountPO::getTelephone, upmUser.getTelephone()));
        return true;
    }

    @Override
    public Long createUser(UpmUser user) {
        UpmUserPO upmUserPO = upmUserConverter.entity2PO(user);
        baseMapper.insert(upmUserPO);
        return upmUserPO.getId();
    }
}
