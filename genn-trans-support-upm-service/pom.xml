<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-trans-support-upm</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-trans-support-upm-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sa-token.version>1.37.0</sa-token.version>
        <jcasbin.version>1.55.0</jcasbin.version>
        <casbin-redis.version>1.5.0</casbin-redis.version>
        <guava-cache.version>30.1.1-jre</guava-cache.version>
        <anji-plus.version>1.3.0</anji-plus.version>
        <feishu-api.version>2.3.5</feishu-api.version>
    </properties>

    <dependencies>
        <!-- ==================== genn 内部依赖   ===================      -->
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-skywalking</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-xxl-job</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.70</version>
        </dependency>


        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.6.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.genn.boot</groupId>-->
<!--            <artifactId>genn-spring-boot-starter-lock</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.genn.boot</groupId>-->
<!--            <artifactId>genn-spring-boot-starter-cache</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.genn.boot</groupId>-->
<!--            <artifactId>genn-spring-boot-starter-lock</artifactId>-->
<!--        </dependency>-->


        <!-- ======================= 服务api依赖  ===========================      -->

        <!-- 自身api依赖        -->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-support-upm-api</artifactId>
            <version>${genn-trans-support-upm-api.version}</version>
        </dependency>
        <!-- 其他项目 api依赖       -->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-pms-api</artifactId>
            <version>${genn-trans-pms-api.version}</version>
        </dependency>

<!--       临时方案，要这玩意儿干嘛？-->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-api-common</artifactId>
            <version>${genn-trans-api-common.version}</version>
        </dependency>


        <!-- ======================= 其他三方依赖   =======================      -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.casbin</groupId>
            <artifactId>jcasbin</artifactId>
            <version>${jcasbin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.casbin</groupId>
            <artifactId>redis-adapter</artifactId>
            <version>${casbin-redis.version}</version>
        </dependency>
        <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        <!-- Sa-Token 插件：整合SSO -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sso</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        <!-- Sa-Token 插件：整合redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava-cache.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <optional>true</optional>
        </dependency>
        <!--验证码anji-plus-->
        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>captcha</artifactId>
            <version>${anji-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-support-ncs-api</artifactId>
            <version>${genn-trans-support-ncs-api.version}</version>
        </dependency>
        <!-- feishu SDK -->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>${feishu-api.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>genn-trans-support-upm</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--  自身和外部api包管理,每次依赖一个新的外部api,在这里定义版本号,dev和test保持 为 genn-service-api.version     -->
        <profile>
            <id>local</id>
            <properties>
                <genn-trans-support-upm-api.version>1.0.0-SNAPSHOT</genn-trans-support-upm-api.version>
                <genn-trans-support-ncs-api.version>1.0.0-SNAPSHOT</genn-trans-support-ncs-api.version>
                <genn-trans-pms-api.version>1.0.2-SNAPSHOT</genn-trans-pms-api.version>
                <genn-trans-api-common.version>1.0.0-SNAPSHOT</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <genn-trans-support-upm-api.version>1.1.9-RELEASE</genn-trans-support-upm-api.version>
                <genn-trans-support-ncs-api.version>1.0.7-RELEASE</genn-trans-support-ncs-api.version>
                <genn-trans-pms-api.version>1.2.5-RELEASE</genn-trans-pms-api.version>
                <genn-trans-api-common.version>1.0.0-RELEASE</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
                <genn-trans-support-ncs-api.version>${genn-service-api.version}</genn-trans-support-ncs-api.version>
                <genn-trans-pms-api.version>${genn-service-api.version}</genn-trans-pms-api.version>
                <genn-trans-api-common.version>${genn-service-api.version}</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
                <genn-trans-support-ncs-api.version>${genn-service-api.version}</genn-trans-support-ncs-api.version>
                <genn-trans-pms-api.version>${genn-service-api.version}</genn-trans-pms-api.version>
                <genn-trans-api-common.version>${genn-service-api.version}</genn-trans-api-common.version>
            </properties>
        </profile>
    </profiles>

</project>
